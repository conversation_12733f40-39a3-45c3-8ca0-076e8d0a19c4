<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美化时间线预览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-200: #68a67d;
            --primary-300: #5a9970;
            --accent-100: #f18f01;
            --text-100: #2d3748;
            --bg-main: #f7fafc;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }

        /* Timeline - 全新美化设计 */
        #timeline-container {
            position: relative;
            padding-left: 2rem;
        }
        
        #timeline-container::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom, 
                var(--primary-200) 0%, 
                var(--accent-100) 50%, 
                var(--primary-300) 100%);
            border-radius: 2px;
            box-shadow: 0 0 10px rgba(104, 166, 125, 0.3);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 3rem;
            padding: 2rem 2rem 2rem 3rem;
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.12) 0%, 
                rgba(255, 255, 255, 0.08) 100%);
            backdrop-filter: blur(25px);
            border-radius: 24px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.1),
                0 2px 8px rgba(0, 0, 0, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            opacity: 0;
            transform: translateY(30px);
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, 
                var(--primary-200), 
                var(--accent-100), 
                var(--primary-200));
            border-radius: 24px 24px 0 0;
        }

        .timeline-item:hover {
            transform: translateY(-8px) scale(1.02);
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.18) 0%, 
                rgba(255, 255, 255, 0.12) 100%);
            border-color: rgba(104, 166, 125, 0.4);
            box-shadow: 
                0 20px 60px rgba(0, 0, 0, 0.15),
                0 8px 24px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .timeline-dot {
            position: absolute;
            left: -3.25rem;
            top: 2rem;
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, 
                var(--primary-200) 0%, 
                var(--primary-300) 50%, 
                var(--accent-100) 100%);
            border: 4px solid rgba(255, 255, 255, 0.9);
            box-shadow: 
                0 0 0 6px rgba(104, 166, 125, 0.2),
                0 8px 24px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .timeline-dot .icon-text {
            font-size: 20px;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            line-height: 1;
            font-weight: 900;
        }

        .timeline-item:hover .timeline-dot {
            transform: scale(1.15) rotate(5deg);
            box-shadow: 
                0 0 0 8px rgba(104, 166, 125, 0.3),
                0 12px 32px rgba(0, 0, 0, 0.2);
        }

        .timeline-header h3 {
            background: linear-gradient(135deg, 
                var(--primary-300) 0%, 
                var(--primary-200) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .timeline-header p {
            color: var(--text-100);
            font-weight: 700;
            font-size: 1.2rem;
        }

        .timeline-header span {
            background: linear-gradient(135deg, 
                rgba(104, 166, 125, 0.1) 0%, 
                rgba(241, 143, 1, 0.1) 100%);
            border: 1px solid rgba(104, 166, 125, 0.3);
            color: var(--primary-300);
            font-weight: 600;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            backdrop-filter: blur(10px);
        }

        .timeline-expand-indicator {
            position: absolute;
            right: 1.5rem;
            top: 50%;
            transform: translateY(-50%);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(104, 166, 125, 0.1);
            border-radius: 50%;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .timeline-expand-indicator:hover {
            background: rgba(104, 166, 125, 0.2);
            transform: translateY(-50%) scale(1.1);
        }

        .job-details {
            max-height: 0;
            opacity: 0;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .achievement-item {
            opacity: 0;
            transform: translateX(-20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.05);
            border-left: 3px solid var(--primary-200);
            padding: 1rem 1.5rem;
            border-radius: 0 12px 12px 0;
            margin-bottom: 1rem;
            backdrop-filter: blur(5px);
        }

        .achievement-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-left-color: var(--accent-100);
            transform: translateX(8px);
        }
    </style>
</head>
<body>
    <div class="container mx-auto px-6 py-12">
        <h1 class="text-4xl font-bold text-white text-center mb-12">✨ 美化时间线预览</h1>
        
        <div class="max-w-4xl mx-auto">
            <div class="relative" id="timeline-container">
                <div class="timeline-item" data-index="0">
                    <div class="timeline-dot">
                        <span class="icon-text">📺</span>
                    </div>
                    <div class="cursor-pointer timeline-header" onclick="toggleTimelineItem(this)">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3">
                            <h3>海信集团有限公司</h3>
                            <span>2021.09 - 2025.06</span>
                        </div>
                        <p>销售总监</p>
                        <div class="timeline-expand-indicator">
                            <i class="fas fa-chevron-down transition-transform duration-300"></i>
                        </div>
                    </div>
                    <div class="job-details mt-6 space-y-4 hidden">
                        <div class="achievement-item">
                            <p><strong>渠道网络建设</strong>：统筹区域经销商网络，制定准入<strong>6大维度评估模型</strong>。</p>
                        </div>
                        <div class="achievement-item">
                            <p><strong>运营管理体系</strong>：建立信息化产品区域推广体系与项目全生命周期管控机制。</p>
                        </div>
                        <div class="achievement-item">
                            <p><strong>业绩</strong>：构建三级分销体系，区域覆盖率从<strong>32%提升至62%</strong>。</p>
                        </div>
                    </div>
                </div>

                <div class="timeline-item" data-index="1">
                    <div class="timeline-dot">
                        <span class="icon-text">🧠</span>
                    </div>
                    <div class="cursor-pointer timeline-header" onclick="toggleTimelineItem(this)">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3">
                            <h3>北京京师讯飞教育科技有限公司</h3>
                            <span>2017.07 - 2021.09</span>
                        </div>
                        <p>市场总监</p>
                        <div class="timeline-expand-indicator">
                            <i class="fas fa-chevron-down transition-transform duration-300"></i>
                        </div>
                    </div>
                    <div class="job-details mt-6 space-y-4 hidden">
                        <div class="achievement-item">
                            <p><strong>品牌推广体系</strong>：制定"展会+数字化"双轮驱动战略。</p>
                        </div>
                        <div class="achievement-item">
                            <p><strong>政企客户开发</strong>：设计教育装备中心业务拓展模型。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 时间线项目切换函数
        function toggleTimelineItem(header) {
            const timelineItem = header.closest('.timeline-item');
            const details = timelineItem.querySelector('.job-details');
            const indicator = header.querySelector('.timeline-expand-indicator i');
            const isExpanded = !details.classList.contains('hidden');

            if (isExpanded) {
                // 收起
                details.style.maxHeight = '0px';
                details.style.opacity = '0';
                setTimeout(() => {
                    details.classList.add('hidden');
                }, 300);
                indicator.style.transform = 'rotate(0deg)';
                timelineItem.classList.remove('expanded');
            } else {
                // 展开
                details.classList.remove('hidden');
                details.style.maxHeight = details.scrollHeight + 'px';
                details.style.opacity = '1';
                indicator.style.transform = 'rotate(180deg)';
                timelineItem.classList.add('expanded');

                // 成就项目的渐入动画
                const achievements = details.querySelectorAll('.achievement-item');
                achievements.forEach((achievement, index) => {
                    setTimeout(() => {
                        achievement.style.opacity = '1';
                        achievement.style.transform = 'translateX(0)';
                    }, index * 100);
                });
            }
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                document.querySelectorAll('.timeline-item').forEach((item, index) => {
                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'translateY(0)';
                    }, index * 200);
                });
            }, 100);
        });
    </script>
</body>
</html>
